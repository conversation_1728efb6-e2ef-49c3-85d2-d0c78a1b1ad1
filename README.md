# 🛡️ CTI Dashboard - Cyber Threat Intelligence Platform

A comprehensive, modular, and scalable Cyber Threat Intelligence (CTI) Dashboard built with FastAPI, featuring AI-powered analysis and automated threat monitoring.

## 🎯 Features

### Core Components

1. **🔍 IoC Repository & Enrichment**
   - Automatic IoC type detection (IP, domain, hash, email, URL)
   - Multi-source enrichment (VirusTotal, AbuseIPDB, URLVoid, Hybrid Analysis)
   - Confidence scoring and threat attribution
   - Batch processing capabilities

2. **🤖 AI-Powered Threat Actor Analysis**
   - LLM integration (OpenAI GPT-4, DeepSeek, Ollama)
   - MITRE ATT&CK framework mapping
   - Automated threat actor profiling
   - Executive summary generation

3. **📡 Passive Intelligence Collection**
   - Shodan, Censys, and ZoomEye integration
   - Service discovery and vulnerability assessment
   - Geolocation and ASN attribution
   - Certificate analysis

4. **⚠️ Watchlist Monitoring**
   - Real-time threat monitoring
   - Flexible matching strategies (exact, partial, subnet)
   - Alert generation and management
   - Automated threat correlation

## 🏗️ Architecture

```
CTI_Dashboard/
├── backend/
│   ├── app/
│   │   ├── core/                    # Core modules
│   │   │   ├── ioc_handler.py       # IoC ingestion & enrichment
│   │   │   ├── actor_summary_agent.py # AI threat actor analysis
│   │   │   ├── passive_scan.py      # Passive intelligence collection
│   │   │   ├── watchlist_monitor.py # Watchlist monitoring & alerts
│   │   │   └── app.py              # FastAPI main application
│   │   ├── models/                  # Database models
│   │   ├── schemas/                 # Pydantic schemas
│   │   ├── api/                     # API endpoints
│   │   └── services/                # External service integrations
│   └── requirements.txt
├── frontend/                        # React/Next.js frontend (future)
├── data/                           # MITRE ATT&CK data & feeds
└── deployment/                     # Docker & K8s configs
```

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- API keys for threat intelligence sources (optional)
- OpenAI API key for AI analysis (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CTI_Dashboard
   ```

2. **Set up Python environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure API keys** (optional)
   ```bash
   # Create .env file with your API keys
   echo "OPENAI_API_KEY=your_openai_key" > .env
   echo "SHODAN_API_KEY=your_shodan_key" >> .env
   echo "VIRUSTOTAL_API_KEY=your_vt_key" >> .env
   ```

4. **Run the application**
   ```bash
   cd app/core
   python app.py
   ```

5. **Access the API**
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

## 📚 API Usage Examples

### IoC Ingestion
```bash
curl -X POST "http://localhost:8000/ioc/ingest" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "malicious-domain.com",
    "source": "threat_feed",
    "tags": ["malware", "c2"]
  }'
```

### Threat Actor Analysis
```bash
curl -X POST "http://localhost:8000/actor/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "APT29",
    "description": "Advanced persistent threat group",
    "ttps": ["T1566", "T1059"],
    "target_industries": ["Government", "Healthcare"]
  }'
```

### Passive Scanning
```bash
curl -X POST "http://localhost:8000/passive/scan" \
  -H "Content-Type: application/json" \
  -d '{
    "target": "*******",
    "scan_type": "ip"
  }'
```

### Watchlist Management
```bash
# Add to watchlist
curl -X POST "http://localhost:8000/watchlist/add" \
  -H "Content-Type: application/json" \
  -d '{
    "value": "suspicious-ip.com",
    "item_type": "domain",
    "description": "Known C2 domain",
    "severity": "high"
  }'

# Get alerts
curl "http://localhost:8000/watchlist/alerts?severity=high"
```

## 🔧 Configuration

### API Keys Configuration
The system supports multiple threat intelligence sources:

- **VirusTotal**: `VIRUSTOTAL_API_KEY`
- **AbuseIPDB**: `ABUSEIPDB_API_KEY`
- **Shodan**: `SHODAN_API_KEY`
- **Censys**: `CENSYS_API_ID`, `CENSYS_API_SECRET`
- **ZoomEye**: `ZOOMEYE_API_KEY`
- **OpenAI**: `OPENAI_API_KEY`

### LLM Configuration
```python
GLOBAL_CONFIG = {
    'llm_service': 'openai',  # 'openai', 'deepseek', 'ollama'
    'openai_model': 'gpt-4',
    'ollama_url': 'http://localhost:11434'
}
```

## 🧪 Testing

Run the test suite:
```bash
pytest backend/tests/
```

## 🐳 Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 🔮 Future Enhancements

- [ ] React/Next.js frontend dashboard
- [ ] PostgreSQL database integration
- [ ] Vector database for RAG-based analysis
- [ ] Real-time WebSocket notifications
- [ ] STIX/TAXII integration
- [ ] Advanced analytics and reporting
- [ ] Multi-tenant support
- [ ] Kubernetes deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- MITRE ATT&CK Framework
- FastAPI community
- Threat intelligence data providers
- Open source security tools

---

**⚠️ Security Note**: This tool is designed for legitimate cybersecurity research and defense purposes. Ensure compliance with applicable laws and regulations when using threat intelligence APIs and data.