<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI Dashboard - Component Demo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid p-4">
        <h1 class="mb-4">CTI Dashboard - UI Components Demo</h1>
        
        <!-- Status Indicators -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Status Indicators</h3>
                <div class="d-flex gap-3 mb-3">
                    <span class="status-indicator online">System Online</span>
                    <span class="status-indicator offline">Service Offline</span>
                    <span class="status-indicator pending">Analysis Pending</span>
                </div>
            </div>
        </div>

        <!-- Metric Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Metric Cards</h3>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value">1,234</div>
                    <div class="metric-label">Total IoCs</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, var(--success-color), #146c43);">
                    <div class="metric-value">98.5%</div>
                    <div class="metric-label">System Uptime</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, var(--warning-color), #e0a800);">
                    <div class="metric-value">42</div>
                    <div class="metric-label">Active Alerts</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card" style="background: linear-gradient(135deg, var(--danger-color), #b02a37);">
                    <div class="metric-value">7</div>
                    <div class="metric-label">Critical Issues</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Alerts -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Enhanced Alerts</h3>
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle"></i>
                    <strong>Success!</strong> IoC analysis completed successfully.
                </div>
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning!</strong> High-risk indicator detected in watchlist.
                </div>
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-shield-exclamation"></i>
                    <strong>Critical!</strong> Malicious activity detected from monitored IP.
                </div>
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle"></i>
                    <strong>Info:</strong> System maintenance scheduled for tonight.
                </div>
            </div>
        </div>

        <!-- Timeline Component -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h3>Activity Timeline</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">IoC Analysis Completed</h6>
                                <p class="card-text">Analyzed IP address ************* with high confidence score.</p>
                                <small class="text-muted">2 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Watchlist Alert Triggered</h6>
                                <p class="card-text">Suspicious domain detected in network traffic.</p>
                                <small class="text-muted">15 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Threat Actor Profile Updated</h6>
                                <p class="card-text">New TTPs added to APT29 profile based on recent intelligence.</p>
                                <small class="text-muted">1 hour ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h3>Search Component</h3>
                <div class="search-container mb-3">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" class="form-control" placeholder="Search indicators, actors, or reports...">
                </div>
                
                <h3>Code Block Example</h3>
                <div class="code-block">
{
  "ioc": {
    "value": "*************",
    "type": "ip",
    "confidence": 0.85,
    "threat_actor": "APT29",
    "tags": ["malicious", "c2"]
  }
}
                </div>
            </div>
        </div>

        <!-- Enhanced Table -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>Enhanced Data Table</h3>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="bi bi-bug"></i> Indicator</th>
                                <th><i class="bi bi-tag"></i> Type</th>
                                <th><i class="bi bi-speedometer"></i> Confidence</th>
                                <th><i class="bi bi-person-x"></i> Threat Actor</th>
                                <th><i class="bi bi-calendar"></i> First Seen</th>
                                <th><i class="bi bi-gear"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>*************</td>
                                <td><span class="badge bg-primary">IP</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" style="width: 85%">85%</div>
                                    </div>
                                </td>
                                <td>APT29</td>
                                <td>2024-01-15</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                    <button class="btn btn-sm btn-outline-warning"><i class="bi bi-pencil"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>malware.example.com</td>
                                <td><span class="badge bg-info">Domain</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-warning" style="width: 65%">65%</div>
                                    </div>
                                </td>
                                <td>Lazarus Group</td>
                                <td>2024-01-14</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                    <button class="btn btn-sm btn-outline-warning"><i class="bi bi-pencil"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>a1b2c3d4e5f6...</td>
                                <td><span class="badge bg-success">Hash</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-danger" style="width: 95%">95%</div>
                                    </div>
                                </td>
                                <td>Unknown</td>
                                <td>2024-01-13</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i></button>
                                    <button class="btn btn-sm btn-outline-warning"><i class="bi bi-pencil"></i></button>
                                    <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading States -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h3>Loading States</h3>
                <div class="card position-relative">
                    <div class="card-body">
                        <h5 class="card-title">Analysis Results</h5>
                        <p class="card-text">This card shows loading overlay when processing...</p>
                        <button class="btn btn-primary" onclick="showLoadingDemo(this)">Show Loading</button>
                    </div>
                    <div class="loading-overlay d-none">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 mb-0">Processing analysis...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h3>Interactive Elements</h3>
                <div class="card clickable mb-3">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi bi-shield-check"></i> Clickable Card</h6>
                        <p class="card-text">This card has hover effects and is interactive.</p>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary">Primary Action</button>
                    <button class="btn btn-outline-secondary">Secondary</button>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            More Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i> Export Data</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-share"></i> Share Report</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear"></i> Settings</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showLoadingDemo(button) {
            const card = button.closest('.card');
            const overlay = card.querySelector('.loading-overlay');
            
            overlay.classList.remove('d-none');
            
            setTimeout(() => {
                overlay.classList.add('d-none');
            }, 3000);
        }
    </script>
</body>
</html>
